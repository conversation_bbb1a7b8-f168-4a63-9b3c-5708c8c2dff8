{"name": "frontend", "version": "1.0.0", "main": "dist-electron/main.js", "scripts": {"dev": "vite", "build": "tsc && vite build", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "electron": "^37.2.1", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "^7.0.4", "wait-on": "^8.0.3"}}