import { useState, useEffect } from 'react'
import './App.css'

interface HealthResponse {
  status: string;
  message: string;
  timestamp: string;
}

function App() {
  const [healthStatus, setHealthStatus] = useState<HealthResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkHealth = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('http://localhost:3001/health');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data: HealthResponse = await response.json();
      setHealthStatus(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Bilinmeyen hata');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkHealth();
  }, []);

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-8 text-gray-800">
          Atorpos POS Sistemi
        </h1>
        
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-2xl font-semibold mb-4 text-gray-700">
            Backend Bağlantı Durumu
          </h2>
          
          <button
            onClick={checkHealth}
            disabled={loading}
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mb-4 disabled:opacity-50"
          >
            {loading ? 'Kontrol Ediliyor...' : 'Bağlantıyı Kontrol Et'}
          </button>

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              <strong>Hata:</strong> {error}
            </div>
          )}

          {healthStatus && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
              <h3 className="font-bold">Backend Durumu: {healthStatus.status}</h3>
              <p>{healthStatus.message}</p>
              <p className="text-sm">Son kontrol: {new Date(healthStatus.timestamp).toLocaleString('tr-TR')}</p>
            </div>
          )}
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-semibold mb-4 text-gray-700">
            Sistem Bilgileri
          </h2>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold">Frontend:</h3>
              <p>Electron + React + TypeScript</p>
            </div>
            <div>
              <h3 className="font-semibold">Backend:</h3>
              <p>Express + TypeScript + Prisma</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default App
