// schema.prisma
// PostgreSQL Database Schema for Restaurant POS System

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ==================== CORE MODELS ====================

model Company {
  id                String   @id @default(cuid())
  name              String
  taxNumber         String   @unique
  taxOffice         String
  address           String
  phone             String
  email             String
  logo              String?
  
  // e-Arşiv bilgileri
  eArchiveUsername  String?
  eArchivePassword  String?  // Encrypted
  
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  branches          Branch[]
  users             User[]
  products          Product[]
  categories        Category[]
  taxes             Tax[]
  paymentMethods    PaymentMethod[]
}

model Branch {
  id                String   @id @default(cuid())
  companyId         String
  code              String   // Şube kodu (örn: "IST01")
  name              String
  address           String
  phone             String
  email             String?
  
  // Network bilgileri (multi-branch sync için)
  serverIp          String?
  serverPort        Int?
  isMainBranch      Boolean  @default(false)
  
  // Çalışma saatleri
  openingTime       String?  // "09:00"
  closingTime       String?  // "23:00"
  
  // Mali bilgiler
  cashRegisterId    String?  // ÖKC cihaz no
  
  active            Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  company           Company  @relation(fields: [companyId], references: [id])
  users             User[]
  tables            Table[]
  orders            Order[]
  cashMovements     CashMovement[]
  stockMovements    StockMovement[]
  priceOverrides    PriceOverride[]
  
  @@unique([companyId, code])
  @@index([companyId])
}

// ==================== USER & AUTH ====================

model User {
  id                String   @id @default(cuid())
  companyId         String
  branchId          String?  // null = tüm şubelere erişim
  username          String   @unique
  password          String   // Hashed
  pin               String?  // Quick login PIN (hashed)
  firstName         String
  lastName          String
  email             String?
  phone             String?
  
  role              UserRole
  permissions       Json?    // Detaylı yetki matrisi
  
  active            Boolean  @default(true)
  lastLoginAt       DateTime?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  company           Company  @relation(fields: [companyId], references: [id])
  branch            Branch?  @relation(fields: [branchId], references: [id])
  orders            Order[]
  cashMovements     CashMovement[]
  sessions          Session[]
  logs              AuditLog[]
  
  @@index([companyId])
  @@index([branchId])
}

enum UserRole {
  SUPER_ADMIN
  ADMIN
  BRANCH_MANAGER
  CASHIER
  WAITER
  KITCHEN
  REPORTER
}

model Session {
  id                String   @id @default(cuid())
  userId            String
  branchId          String
  token             String   @unique
  deviceInfo        String?  // PC adı, IP vs.
  
  startedAt         DateTime @default(now())
  endedAt           DateTime?
  lastActivityAt    DateTime @default(now())
  
  user              User     @relation(fields: [userId], references: [id])
  
  @@index([userId])
  @@index([token])
}

// ==================== PRODUCT MANAGEMENT ====================

model Category {
  id                String   @id @default(cuid())
  companyId         String
  parentId          String?  // Hiyerarşik kategoriler
  
  name              String
  description       String?
  image             String?
  color             String?  // UI renk kodu
  icon              String?  // Icon adı
  
  displayOrder      Int      @default(0)
  active            Boolean  @default(true)
  
  // Kategori bazlı ayarlar
  printerGroupId    String?  // Hangi yazıcıya gidecek
  preparationTime   Int?     // Dakika cinsinden
  
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  company           Company  @relation(fields: [companyId], references: [id])
  parent            Category? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children          Category[] @relation("CategoryHierarchy")
  products          Product[]
  printerGroup      PrinterGroup? @relation(fields: [printerGroupId], references: [id])
  
  @@index([companyId])
  @@index([parentId])
}

model Product {
  id                String   @id @default(cuid())
  companyId         String
  categoryId        String
  
  code              String   // Ürün kodu
  barcode           String?  // Barkod
  name              String
  description       String?
  image             String?
  
  // Fiyatlandırma - Decimal kullanımı
  basePrice         Decimal  @db.Decimal(10, 2)
  taxId             String
  
  // Stok takibi
  trackStock        Boolean  @default(false)
  unit              ProductUnit @default(PIECE)
  criticalStock     Decimal? @db.Decimal(10, 3)
  
  // Satış ayarları
  available         Boolean  @default(true)
  sellable          Boolean  @default(true)
  preparationTime   Int?     // Dakika
  
  // Porsiyon/Varyant desteği
  hasVariants       Boolean  @default(false)
  hasModifiers      Boolean  @default(false)
  
  displayOrder      Int      @default(0)
  active            Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  // Senkronizasyon için
  syncId            String?  @unique // Multi-branch sync
  lastSyncAt        DateTime?
  
  company           Company  @relation(fields: [companyId], references: [id])
  category          Category @relation(fields: [categoryId], references: [id])
  tax               Tax      @relation(fields: [taxId], references: [id])
  
  variants          ProductVariant[]
  modifierGroups    ProductModifierGroup[]
  recipes           Recipe[]
  orderItems        OrderItem[]
  stockMovements    StockMovement[]
  priceOverrides    PriceOverride[]
  inventoryItems    InventoryItem[]
  
  @@unique([companyId, code])
  @@index([companyId])
  @@index([categoryId])
  @@index([barcode])
}

enum ProductUnit {
  PIECE     // Adet
  KG        // Kilogram
  GRAM      // Gram
  LITER     // Litre
  ML        // Mililitre
  PORTION   // Porsiyon
}

model ProductVariant {
  id                String   @id @default(cuid())
  productId         String
  
  name              String   // "Küçük", "Orta", "Büyük"
  code              String   // "S", "M", "L"
  price             Decimal  @db.Decimal(10, 2)
  
  displayOrder      Int      @default(0)
  active            Boolean  @default(true)
  
  product           Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  orderItems        OrderItem[]
  
  @@index([productId])
}

model ModifierGroup {
  id                String   @id @default(cuid())
  name              String   // "Sos Seçimi", "Pişirme Derecesi"
  
  minSelection      Int      @default(0)
  maxSelection      Int      @default(1)
  required          Boolean  @default(false)
  
  displayOrder      Int      @default(0)
  active            Boolean  @default(true)
  
  modifiers         Modifier[]
  products          ProductModifierGroup[]
}

model Modifier {
  id                String   @id @default(cuid())
  groupId           String
  
  name              String   // "Ketçap", "Az Pişmiş"
  price             Decimal  @db.Decimal(10, 2) @default(0)
  
  displayOrder      Int      @default(0)
  active            Boolean  @default(true)
  
  group             ModifierGroup @relation(fields: [groupId], references: [id], onDelete: Cascade)
  orderItemModifiers OrderItemModifier[]
  
  @@index([groupId])
}

model ProductModifierGroup {
  productId         String
  modifierGroupId   String
  
  displayOrder      Int      @default(0)
  
  product           Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  modifierGroup     ModifierGroup @relation(fields: [modifierGroupId], references: [id])
  
  @@id([productId, modifierGroupId])
}

// ==================== INVENTORY & RECIPES ====================

model InventoryItem {
  id                String   @id @default(cuid())
  productId         String?  // Eğer bu bir satılabilir ürünse
  
  name              String
  code              String   @unique
  unit              ProductUnit
  
  currentStock      Decimal  @db.Decimal(10, 3)
  criticalLevel     Decimal? @db.Decimal(10, 3)
  
  // Maliyet takibi
  lastCost          Decimal? @db.Decimal(10, 2)
  averageCost       Decimal? @db.Decimal(10, 2)
  
  active            Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  product           Product? @relation(fields: [productId], references: [id])
  recipeItems       RecipeItem[]
  stockMovements    StockMovement[]
}

model Recipe {
  id                String   @id @default(cuid())
  productId         String
  
  name              String
  yield             Decimal  @db.Decimal(10, 3) // Kaç porsiyon/adet
  
  active            Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  product           Product  @relation(fields: [productId], references: [id])
  items             RecipeItem[]
}

model RecipeItem {
  id                String   @id @default(cuid())
  recipeId          String
  inventoryItemId   String
  
  quantity          Decimal  @db.Decimal(10, 3)
  unit              ProductUnit
  
  recipe            Recipe   @relation(fields: [recipeId], references: [id], onDelete: Cascade)
  inventoryItem     InventoryItem @relation(fields: [inventoryItemId], references: [id])
  
  @@index([recipeId])
  @@index([inventoryItemId])
}

// ==================== TABLE MANAGEMENT ====================

model TableArea {
  id                String   @id @default(cuid())
  branchId          String
  
  name              String   // "Salon", "Bahçe", "Teras"
  displayOrder      Int      @default(0)
  active            Boolean  @default(true)
  
  tables            Table[]
}

model Table {
  id                String   @id @default(cuid())
  branchId          String
  areaId            String?
  
  number            String   // "1", "A1", "BAR-3"
  capacity          Int      @default(4)
  
  // Görsel düzenleme için
  positionX         Int?
  positionY         Int?
  width             Int?
  height            Int?
  shape             TableShape @default(RECTANGLE)
  
  status            TableStatus @default(EMPTY)
  active            Boolean  @default(true)
  
  branch            Branch   @relation(fields: [branchId], references: [id])
  area              TableArea? @relation(fields: [areaId], references: [id])
  orders            Order[]
  
  @@unique([branchId, number])
  @@index([branchId])
}

enum TableShape {
  RECTANGLE
  CIRCLE
  SQUARE
}

enum TableStatus {
  EMPTY
  OCCUPIED
  RESERVED
  CLEANING
  UNAVAILABLE
}

// ==================== ORDER MANAGEMENT ====================

model Order {
  id                String   @id @default(cuid())
  branchId          String
  orderNumber       String   // "2024-0001"
  
  // Sipariş tipi ve masa
  orderType         OrderType
  tableId           String?
  customerCount     Int?
  
  // Müşteri bilgileri
  customerId        String?
  customerName      String?
  customerPhone     String?
  deliveryAddress   String?
  
  // Durumlar
  status            OrderStatus @default(PENDING)
  paymentStatus     PaymentStatus @default(UNPAID)
  
  // Tutarlar - Decimal kullanımı
  subtotal          Decimal  @db.Decimal(10, 2)
  discountAmount    Decimal  @db.Decimal(10, 2) @default(0)
  discountRate      Decimal  @db.Decimal(5, 2) @default(0)
  taxAmount         Decimal  @db.Decimal(10, 2)
  totalAmount       Decimal  @db.Decimal(10, 2)
  paidAmount        Decimal  @db.Decimal(10, 2) @default(0)
  tipAmount         Decimal  @db.Decimal(10, 2) @default(0)
  
  // Personel
  waiterId          String?
  cashierId         String?
  
  // Notlar
  orderNote         String?
  kitchenNote       String?
  
  // Zaman damgaları
  orderedAt         DateTime @default(now())
  completedAt       DateTime?
  cancelledAt       DateTime?
  
  // Senkronizasyon
  syncId            String?  @unique
  lastSyncAt        DateTime?
  
  branch            Branch   @relation(fields: [branchId], references: [id])
  table             Table?   @relation(fields: [tableId], references: [id])
  customer          Customer? @relation(fields: [customerId], references: [id])
  waiter            User?    @relation(fields: [waiterId], references: [id])
  
  items             OrderItem[]
  payments          Payment[]
  invoice           Invoice?
  logs              OrderLog[]
  
  @@unique([branchId, orderNumber])
  @@index([branchId])
  @@index([status])
  @@index([orderedAt])
}

enum OrderType {
  DINE_IN       // Restoranda
  TAKEAWAY      // Paket
  DELIVERY      // Eve servis
  ONLINE        // Online sipariş
}

enum OrderStatus {
  PENDING       // Bekliyor
  CONFIRMED     // Onaylandı
  PREPARING     // Hazırlanıyor
  READY         // Hazır
  DELIVERING    // Teslimatta
  COMPLETED     // Tamamlandı
  CANCELLED     // İptal
}

enum PaymentStatus {
  UNPAID        // Ödenmedi
  PARTIAL       // Kısmi ödendi
  PAID          // Ödendi
  REFUNDED      // İade edildi
}

model OrderItem {
  id                String   @id @default(cuid())
  orderId           String
  productId         String
  variantId         String?
  
  quantity          Decimal  @db.Decimal(10, 3)
  unitPrice         Decimal  @db.Decimal(10, 2)
  discountAmount    Decimal  @db.Decimal(10, 2) @default(0)
  taxRate           Decimal  @db.Decimal(5, 2)
  taxAmount         Decimal  @db.Decimal(10, 2)
  totalAmount       Decimal  @db.Decimal(10, 2)
  
  // Durum takibi
  status            OrderItemStatus @default(PENDING)
  
  // Mutfak takibi
  sentToKitchenAt   DateTime?
  startedAt         DateTime?
  completedAt       DateTime?
  servedAt          DateTime?
  cancelledAt       DateTime?
  
  note              String?
  
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  order             Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product           Product  @relation(fields: [productId], references: [id])
  variant           ProductVariant? @relation(fields: [variantId], references: [id])
  modifiers         OrderItemModifier[]
  
  @@index([orderId])
  @@index([status])
}

enum OrderItemStatus {
  PENDING       // Bekliyor
  SENT          // Mutfağa gönderildi
  PREPARING     // Hazırlanıyor
  READY         // Hazır
  SERVED        // Servis edildi
  CANCELLED     // İptal
  VOID          // Geçersiz
}

model OrderItemModifier {
  id                String   @id @default(cuid())
  orderItemId       String
  modifierId        String
  
  quantity          Int      @default(1)
  price             Decimal  @db.Decimal(10, 2)
  
  orderItem         OrderItem @relation(fields: [orderItemId], references: [id], onDelete: Cascade)
  modifier          Modifier @relation(fields: [modifierId], references: [id])
  
  @@index([orderItemId])
}

// ==================== PAYMENT & FINANCE ====================

model PaymentMethod {
  id                String   @id @default(cuid())
  companyId         String
  
  name              String   // "Nakit", "Kredi Kartı", "Yemek Kartı"
  code              String   // "CASH", "CC", "MEAL"
  type              PaymentMethodType
  
  // Komisyon oranları
  commissionRate    Decimal  @db.Decimal(5, 2) @default(0)
  
  requiresApproval  Boolean  @default(false)
  displayOrder      Int      @default(0)
  active            Boolean  @default(true)
  
  company           Company  @relation(fields: [companyId], references: [id])
  payments          Payment[]
  
  @@unique([companyId, code])
}

enum PaymentMethodType {
  CASH
  CREDIT_CARD
  DEBIT_CARD
  MEAL_CARD
  MOBILE
  TRANSFER
  OTHER
}

model Payment {
  id                String   @id @default(cuid())
  orderId           String
  paymentMethodId   String
  
  amount            Decimal  @db.Decimal(10, 2)
  tipAmount         Decimal  @db.Decimal(10, 2) @default(0)
  
  // Kart ödemeleri için
  approvalCode      String?
  maskedCardNumber  String?  // "****1234"
  
  status            PaymentStatus
  
  paidAt            DateTime @default(now())
  refundedAt        DateTime?
  
  // Kasa hareketi bağlantısı
  cashMovementId    String?
  
  order             Order    @relation(fields: [orderId], references: [id])
  paymentMethod     PaymentMethod @relation(fields: [paymentMethodId], references: [id])
  
  @@index([orderId])
}

model Tax {
  id                String   @id @default(cuid())
  companyId         String
  
  name              String   // "KDV %8", "KDV %18"
  rate              Decimal  @db.Decimal(5, 2)
  code              String   // "VAT8", "VAT18"
  
  isDefault         Boolean  @default(false)
  active            Boolean  @default(true)
  
  company           Company  @relation(fields: [companyId], references: [id])
  products          Product[]
  
  @@unique([companyId, code])
}

// ==================== INVOICE & LEGAL ====================

model Invoice {
  id                String   @id @default(cuid())
  orderId           String   @unique
  invoiceType       InvoiceType
  
  // Fatura numarası
  serialNo          String   // "A"
  sequenceNo        String   // "2024000001"
  
  // Müşteri bilgileri
  customerName      String?
  customerTaxNo     String?
  customerTaxOffice String?
  customerAddress   String?
  customerPhone     String?
  customerEmail     String?
  
  // Tutarlar
  subtotal          Decimal  @db.Decimal(10, 2)
  taxAmount         Decimal  @db.Decimal(10, 2)
  totalAmount       Decimal  @db.Decimal(10, 2)
  
  // e-Arşiv
  uuid              String?  @unique
  eArchiveStatus    EArchiveStatus?
  eArchiveResponse  Json?
  
  createdAt         DateTime @default(now())
  printedAt         DateTime?
  cancelledAt       DateTime?
  
  order             Order    @relation(fields: [orderId], references: [id])
  
  @@unique([serialNo, sequenceNo])
  @@index([createdAt])
}

enum InvoiceType {
  RECEIPT       // Perakende satış fişi
  INVOICE       // Fatura
  E_ARCHIVE     // e-Arşiv fatura
  E_INVOICE     // e-Fatura
}

enum EArchiveStatus {
  PENDING
  SENT
  APPROVED
  REJECTED
  CANCELLED
}

// ==================== CASH & ACCOUNTING ====================

model CashMovement {
  id                String   @id @default(cuid())
  branchId          String
  userId            String
  
  type              CashMovementType
  amount            Decimal  @db.Decimal(10, 2)
  
  description       String
  referenceId       String?  // Order ID, expense ID vs.
  referenceType     String?  // "ORDER", "EXPENSE" vs.
  
  // Kasa durumu
  previousBalance   Decimal  @db.Decimal(10, 2)
  currentBalance    Decimal  @db.Decimal(10, 2)
  
  createdAt         DateTime @default(now())
  
  branch            Branch   @relation(fields: [branchId], references: [id])
  user              User     @relation(fields: [userId], references: [id])
  
  @@index([branchId])
  @@index([createdAt])
}

enum CashMovementType {
  SALE          // Satış
  REFUND        // İade
  EXPENSE       // Gider
  INCOME        // Gelir
  OPENING       // Açılış
  CLOSING       // Kapanış
  DEPOSIT       // Para yatırma
  WITHDRAWAL    // Para çekme
}

model DailyReport {
  id                String   @id @default(cuid())
  branchId          String
  reportDate        DateTime @db.Date
  
  // Satış özeti
  totalOrders       Int
  totalItems        Int
  grossSales        Decimal  @db.Decimal(10, 2)
  totalDiscount     Decimal  @db.Decimal(10, 2)
  netSales          Decimal  @db.Decimal(10, 2)
  totalTax          Decimal  @db.Decimal(10, 2)
  totalSales        Decimal  @db.Decimal(10, 2)
  
  // Ödeme dağılımı
  cashSales         Decimal  @db.Decimal(10, 2)
  creditCardSales   Decimal  @db.Decimal(10, 2)
  otherSales        Decimal  @db.Decimal(10, 2)
  
  // Kasa durumu
  openingBalance    Decimal  @db.Decimal(10, 2)
  closingBalance    Decimal  @db.Decimal(10, 2)
  
  // Z raporu
  zReportNo         String?
  zReportTime       DateTime?
  
  createdAt         DateTime @default(now())
  createdBy         String
  
  @@unique([branchId, reportDate])
  @@index([branchId])
  @@index([reportDate])
}

// ==================== INVENTORY & STOCK ====================

model StockMovement {
  id                String   @id @default(cuid())
  branchId          String
  productId         String?
  inventoryItemId   String?
  
  type              StockMovementType
  quantity          Decimal  @db.Decimal(10, 3)
  unit              ProductUnit
  
  // Maliyet
  unitCost          Decimal? @db.Decimal(10, 2)
  totalCost         Decimal? @db.Decimal(10, 2)
  
  // Referans
  referenceId       String?  // Order ID, transfer ID vs.
  referenceType     String?
  
  note              String?
  
  createdAt         DateTime @default(now())
  createdBy         String
  
  branch            Branch   @relation(fields: [branchId], references: [id])
  product           Product? @relation(fields: [productId], references: [id])
  inventoryItem     InventoryItem? @relation(fields: [inventoryItemId], references: [id])
  
  @@index([branchId])
  @@index([productId])
  @@index([inventoryItemId])
  @@index([createdAt])
}

enum StockMovementType {
  PURCHASE      // Alım
  SALE          // Satış
  RETURN        // İade
  WASTE         // Fire
  TRANSFER_IN   // Transfer girişi
  TRANSFER_OUT  // Transfer çıkışı
  ADJUSTMENT    // Sayım düzeltme
  PRODUCTION    // Üretim
  CONSUMPTION   // Tüketim
}

// ==================== CUSTOMER MANAGEMENT ====================

model Customer {
  id                String   @id @default(cuid())
  
  // Kişisel bilgiler
  firstName         String?
  lastName          String?
  companyName       String?
  taxNumber         String?
  taxOffice         String?
  
  // İletişim
  phone             String   @unique
  email             String?
  address           String?
  
  // Sadakat programı
  loyaltyPoints     Int      @default(0)
  totalSpent        Decimal  @db.Decimal(10, 2) @default(0)
  orderCount        Int      @default(0)
  
  // Borç takibi
  currentDebt       Decimal  @db.Decimal(10, 2) @default(0)
  creditLimit       Decimal  @db.Decimal(10, 2) @default(0)
  
  notes             String?
  
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  orders            Order[]
  
  @@index([phone])
  @@index([email])
}

// ==================== SETTINGS & CONFIGURATION ====================

model Printer {
  id                String   @id @default(cuid())
  branchId          String
  
  name              String
  ipAddress         String?
  port              Int?
  type              PrinterType
  
  paperWidth        Int      @default(80) // mm
  
  active            Boolean  @default(true)
  
  printerGroups     PrinterGroup[]
}

enum PrinterType {
  RECEIPT       // Fiş yazıcısı
  KITCHEN       // Mutfak yazıcısı  
  LABEL         // Etiket yazıcısı
}

model PrinterGroup {
  id                String   @id @default(cuid())
  printerId         String
  name              String   // "Ana Mutfak", "Bar"
  
  printer           Printer  @relation(fields: [printerId], references: [id])
  categories        Category[]
}

// ==================== SPECIAL FEATURES ====================

model PriceOverride {
  id                String   @id @default(cuid())
  productId         String
  branchId          String?  // null = tüm şubeler
  
  price             Decimal  @db.Decimal(10, 2)
  
  startDate         DateTime?
  endDate           DateTime?
  
  // Happy hour gibi durumlar için
  startTime         String?  // "17:00"
  endTime           String?  // "19:00"
  daysOfWeek        Int[]    // [1,2,3,4,5] = Pazartesi-Cuma
  
  active            Boolean  @default(true)
  
  product           Product  @relation(fields: [productId], references: [id])
  branch            Branch?  @relation(fields: [branchId], references: [id])
  
  @@index([productId])
  @@index([branchId])
}

// ==================== LOGGING & AUDIT ====================

model AuditLog {
  id                String   @id @default(cuid())
  userId            String?
  branchId          String?
  
  action            String   // "ORDER_CREATED", "PAYMENT_RECEIVED"
  entityType        String   // "Order", "Payment"
  entityId          String
  
  oldValues         Json?
  newValues         Json?
  
  ipAddress         String?
  userAgent         String?
  
  createdAt         DateTime @default(now())
  
  user              User?    @relation(fields: [userId], references: [id])
  
  @@index([entityType, entityId])
  @@index([createdAt])
  @@index([userId])
}

model OrderLog {
  id                String   @id @default(cuid())
  orderId           String
  
  action            String   // "STATUS_CHANGED", "ITEM_ADDED"
  details           Json
  
  createdAt         DateTime @default(now())
  createdBy         String?
  
  order             Order    @relation(fields: [orderId], references: [id])
  
  @@index([orderId])
}

// ==================== SYNC & MIGRATION ====================

model SyncLog {
  id                String   @id @default(cuid())
  branchId          String
  
  syncType          String   // "FULL", "PARTIAL"
  direction         String   // "UPLOAD", "DOWNLOAD"
  
  recordCount       Int
  successCount      Int
  failureCount      Int
  
  startedAt         DateTime
  completedAt       DateTime?
  
  error             String?
  details           Json?
  
  @@index([branchId])
  @@index([startedAt])
}

